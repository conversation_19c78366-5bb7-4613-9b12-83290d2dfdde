<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Previous/Next example</title>
</head>
<body>

<h1>'Previous/Next' example</h1>

<div>
  <button id="prev" type="button">Previous</button>
  <button id="next" type="button">Next</button>
  &nbsp; &nbsp;
  <span>Page: <span id="page_num"></span> / <span id="page_count"></span></span>
</div>

<div>
  <canvas id="the-canvas" style="border: 1px solid black; direction: ltr;"></canvas>
</div>

<script src="../../node_modules/pdfjs-dist/build/pdf.mjs" type="module"></script>

<script id="script" type="module">
  //
  // If absolute URL from the remote server is provided, configure the CORS
  // header on that server.
  //
  var url = '../../web/compressed.tracemonkey-pldi-09.pdf';

  //
  // In cases when the pdf.worker.js is located at the different folder than the
  // PDF.js's one, or the PDF.js is executed via eval(), the workerSrc property
  // shall be specified.
  //
  pdfjsLib.GlobalWorkerOptions.workerSrc =
    '../../node_modules/pdfjs-dist/build/pdf.worker.mjs';

  var pdfDoc = null,
      pageNum = 1,
      pageRendering = false,
      pageNumPending = null,
      scale = 0.8,
      canvas = document.getElementById('the-canvas'),
      ctx = canvas.getContext('2d');

  /**
   * Get page info from document, resize canvas accordingly, and render page.
   * @param num Page number.
   */
  function renderPage(num) {
    pageRendering = true;
    // Using promise to fetch the page
    pdfDoc.getPage(num).then(function(page) {
      var viewport = page.getViewport({ scale: scale, });
      // Support HiDPI-screens.
      var outputScale = window.devicePixelRatio || 1;

      canvas.width = Math.floor(viewport.width * outputScale);
      canvas.height = Math.floor(viewport.height * outputScale);
      canvas.style.width = Math.floor(viewport.width) + "px";
      canvas.style.height =  Math.floor(viewport.height) + "px";

      var transform = outputScale !== 1
        ? [outputScale, 0, 0, outputScale, 0, 0]
        : null;

      // Render PDF page into canvas context
      var renderContext = {
        canvasContext: ctx,
        transform: transform,
        viewport: viewport,
      };
      var renderTask = page.render(renderContext);

      // Wait for rendering to finish
      renderTask.promise.then(function () {
        pageRendering = false;
        if (pageNumPending !== null) {
          // New page rendering is pending
          renderPage(pageNumPending);
          pageNumPending = null;
        }
      });
    });

    // Update page counters
    document.getElementById('page_num').textContent = num;
  }

  /**
   * If another page rendering in progress, waits until the rendering is
   * finished. Otherwise, executes rendering immediately.
   */
  function queueRenderPage(num) {
    if (pageRendering) {
      pageNumPending = num;
    } else {
      renderPage(num);
    }
  }

  /**
   * Displays previous page.
   */
  function onPrevPage() {
    if (pageNum <= 1) {
      return;
    }
    pageNum--;
    queueRenderPage(pageNum);
  }
  document.getElementById('prev').addEventListener('click', onPrevPage);

  /**
   * Displays next page.
   */
  function onNextPage() {
    if (pageNum >= pdfDoc.numPages) {
      return;
    }
    pageNum++;
    queueRenderPage(pageNum);
  }
  document.getElementById('next').addEventListener('click', onNextPage);

  /**
   * Asynchronously downloads PDF.
   */
  var loadingTask = pdfjsLib.getDocument(url);
  pdfDoc = await loadingTask.promise;
  document.getElementById('page_count').textContent = pdfDoc.numPages;

  // Initial/first page rendering
  renderPage(pageNum);
</script>

</body>
</html>
