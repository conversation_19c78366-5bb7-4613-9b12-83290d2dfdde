# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.


## Main toolbar buttons (tooltips and alt text for images)

pdfjs-zoom-out-button =
    .title = <PERSON>chiù nicu
pdfjs-zoom-out-button-label = <PERSON>chiù nicu
pdfjs-zoom-in-button =
    .title = Cchiù granni
pdfjs-zoom-in-button-label = Cchiù granni

##

# The linearization status of the document; usually called "Fast Web View" in
# English locales of Adobe software.
pdfjs-document-properties-linearized = Vista web lesta:
pdfjs-document-properties-linearized-yes = Se

## Print

pdfjs-print-progress-close-button = Sfai

## Predefined zoom values

pdfjs-page-scale-width = Larghizza dâ pàggina

## Password

pdfjs-password-cancel-button = Sfai
