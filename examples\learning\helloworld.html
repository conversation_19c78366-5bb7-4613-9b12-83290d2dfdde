<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>'Hello, world!' example</title>
</head>
<body>

<h1>'Hello, world!' example</h1>

<canvas id="the-canvas" style="border: 1px solid black; direction: ltr;"></canvas>

<script src="../../node_modules/pdfjs-dist/build/pdf.mjs" type="module"></script>

<script id="script" type="module">
  //
  // If absolute URL from the remote server is provided, configure the CORS
  // header on that server.
  //
  const url = './helloworld.pdf';

  //
  // The workerSrc property shall be specified.
  //
  pdfjsLib.GlobalWorkerOptions.workerSrc =
    '../../node_modules/pdfjs-dist/build/pdf.worker.mjs';

  //
  // Asynchronous download PDF
  //
  const loadingTask = pdfjsLib.getDocument(url);
  const pdf = await loadingTask.promise;
  //
  // Fetch the first page
  //
  const page = await pdf.getPage(1);
  const scale = 1.5;
  const viewport = page.getViewport({ scale });
  // Support HiDPI-screens.
  const outputScale = window.devicePixelRatio || 1;

  //
  // Prepare canvas using PDF page dimensions
  //
  const canvas = document.getElementById("the-canvas");
  const context = canvas.getContext("2d");

  canvas.width = Math.floor(viewport.width * outputScale);
  canvas.height = Math.floor(viewport.height * outputScale);
  canvas.style.width = Math.floor(viewport.width) + "px";
  canvas.style.height = Math.floor(viewport.height) + "px";

  const transform = outputScale !== 1
    ? [outputScale, 0, 0, outputScale, 0, 0]
    : null;

  //
  // Render PDF page into canvas context
  //
  const renderContext = {
    canvasContext: context,
    transform,
    viewport,
  };
  page.render(renderContext);
</script>

<hr>
<h2>JavaScript code:</h2>
<pre id="code"></pre>
<script>
  document.getElementById('code').textContent =
      document.getElementById('script').text;
</script>
</body>
</html>
