name: <PERSON><PERSON>
on: [push, pull_request]
permissions:
  contents: read

jobs:
  lint:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        node-version: [lts/*]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install dependencies
        run: npm ci

      - name: Run lint
        run: npx gulp lint

      - name: Run lint-chromium
        run: npx gulp lint-chromium
