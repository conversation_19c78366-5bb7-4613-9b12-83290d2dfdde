/* Copyright 2024 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * Text replacement functionality for PDF.js
 * Provides dynamic text replacement based on configurable rules
 */

class TextReplacer {
  constructor(rules) {
    this.rules = rules || [];
    this.cache = new Map(); // Cache replacement results for performance
    this.stats = {
      totalReplacements: 0,
      cacheHits: 0,
      cacheMisses: 0
    };
  }

  /**
   * Apply text replacement rules to input text
   * @param {string} text - Original text
   * @returns {string} - Text after applying replacement rules
   */
  applyRules(text) {
    if (!text || !this.rules.length) return text;
    
    // Check cache first
    if (this.cache.has(text)) {
      this.stats.cacheHits++;
      return this.cache.get(text);
    }

    this.stats.cacheMisses++;
    let result = text;
    
    for (const rule of this.rules) {
      const beforeLength = result.length;
      result = this.applyRule(result, rule);
      
      // Count replacements
      if (result !== text || result.length !== beforeLength) {
        this.stats.totalReplacements++;
      }
    }
    
    // Cache result (limit cache size to prevent memory issues)
    if (this.cache.size < 10000) {
      this.cache.set(text, result);
    }
    
    return result;
  }

  /**
   * Apply a single replacement rule
   * @param {string} text - Input text
   * @param {Object} rule - Replacement rule
   * @returns {string} - Text after applying the rule
   */
  applyRule(text, rule) {
    const { type, from, to, caseSensitive, global } = rule;
    
    try {
      switch (type) {
        case 'exact':
          return this.exactReplace(text, from, to, caseSensitive, global);
        case 'regex':
          return this.regexReplace(text, from, to, caseSensitive, global);
        case 'partial':
          return this.partialReplace(text, from, to, caseSensitive, global);
        default:
          console.warn('Unknown replacement rule type:', type);
          return text;
      }
    } catch (error) {
      console.warn('Error applying replacement rule:', rule, error);
      return text;
    }
  }

  /**
   * Exact text replacement
   * @param {string} text - Input text
   * @param {string} from - Text to replace
   * @param {string} to - Replacement text
   * @param {boolean} caseSensitive - Case sensitive matching
   * @param {boolean} global - Replace all occurrences
   * @returns {string} - Replaced text
   */
  exactReplace(text, from, to, caseSensitive, global) {
    if (!from) return text;
    
    if (!caseSensitive) {
      const flags = global ? 'gi' : 'i';
      const regex = new RegExp(this.escapeRegex(from), flags);
      return text.replace(regex, to);
    }
    
    return global ? text.replaceAll(from, to) : text.replace(from, to);
  }

  /**
   * Regular expression replacement
   * @param {string} text - Input text
   * @param {string} pattern - Regex pattern
   * @param {string} to - Replacement text
   * @param {boolean} caseSensitive - Case sensitive matching
   * @param {boolean} global - Replace all occurrences
   * @returns {string} - Replaced text
   */
  regexReplace(text, pattern, to, caseSensitive, global) {
    if (!pattern) return text;
    
    try {
      const flags = (caseSensitive ? '' : 'i') + (global ? 'g' : '');
      const regex = new RegExp(pattern, flags);
      return text.replace(regex, to);
    } catch (error) {
      console.warn('Invalid regex pattern:', pattern, error);
      return text;
    }
  }

  /**
   * Partial text replacement (contains matching)
   * @param {string} text - Input text
   * @param {string} from - Text to find
   * @param {string} to - Replacement text
   * @param {boolean} caseSensitive - Case sensitive matching
   * @param {boolean} global - Replace all occurrences
   * @returns {string} - Replaced text
   */
  partialReplace(text, from, to, caseSensitive, global) {
    if (!from) return text;
    
    const flags = (caseSensitive ? '' : 'i') + (global ? 'g' : '');
    const regex = new RegExp(this.escapeRegex(from), flags);
    return text.replace(regex, to);
  }

  /**
   * Escape special regex characters
   * @param {string} string - String to escape
   * @returns {string} - Escaped string
   */
  escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Clear the replacement cache
   */
  clearCache() {
    this.cache.clear();
    this.stats.cacheHits = 0;
    this.stats.cacheMisses = 0;
  }

  /**
   * Get replacement statistics
   * @returns {Object} - Statistics object
   */
  getStats() {
    return {
      ...this.stats,
      cacheSize: this.cache.size,
      rulesCount: this.rules.length
    };
  }

  /**
   * Update replacement rules
   * @param {Array} newRules - New replacement rules
   */
  updateRules(newRules) {
    this.rules = newRules || [];
    this.clearCache(); // Clear cache when rules change
  }

  /**
   * Check if text replacement is enabled and has rules
   * @returns {boolean} - True if replacement is active
   */
  isActive() {
    return this.rules && this.rules.length > 0;
  }

  /**
   * Validate a replacement rule
   * @param {Object} rule - Rule to validate
   * @returns {boolean} - True if rule is valid
   */
  static validateRule(rule) {
    if (!rule || typeof rule !== 'object') return false;
    
    const validTypes = ['exact', 'regex', 'partial'];
    if (!validTypes.includes(rule.type)) return false;
    
    if (typeof rule.from !== 'string' || !rule.from) return false;
    if (typeof rule.to !== 'string') return false;
    
    // Test regex pattern if type is regex
    if (rule.type === 'regex') {
      try {
        new RegExp(rule.from);
      } catch (error) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * Create a TextReplacer instance from AppOptions
   * @returns {TextReplacer|null} - TextReplacer instance or null if disabled
   */
  static fromAppOptions() {
    // This will be called from text_layer.js where AppOptions might not be available
    // We'll handle this in the integration
    return null;
  }
}

export { TextReplacer };
