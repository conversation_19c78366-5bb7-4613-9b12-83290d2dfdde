name: CI
on: [push, pull_request]
permissions:
  contents: read

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        node-version: [20, 22, 24]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install dependencies
        run: npm ci

      - name: Run external tests
        run: npx gulp externaltest

      - name: Run CLI unit tests
        run: npx gulp unittestcli
