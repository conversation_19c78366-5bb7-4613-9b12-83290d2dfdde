<!DOCTYPE html>
<!--
Copyright 2024 PDF.js Contributors

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->
<html>
<head>
    <meta charset="utf-8">
    <title>PDF.js External Highlights Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .demo-section h2 {
            margin-top: 0;
            color: #333;
        }
        .highlight-example {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 3px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .demo-link {
            display: inline-block;
            margin: 5px 10px 5px 0;
            padding: 8px 15px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 3px;
            font-size: 14px;
        }
        .demo-link:hover {
            background-color: #0056b3;
        }
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>PDF.js External Highlights Demo</h1>
    
    <div class="note">
        <strong>Note:</strong> This demo shows how to use the external highlight feature in PDF.js. 
        You need to have a PDF file to test with. The highlights are passed via URL parameters.
    </div>

    <div class="demo-section">
        <h2>Basic Usage</h2>
        <p>To add highlights to a PDF, append the <code>highlights</code> parameter to the viewer URL with JSON data:</p>
        <div class="highlight-example">viewer.html?file=your-pdf.pdf&highlights=[{"page":1,"x1":100,"y1":200,"x2":400,"y2":250}]</div>
        
        <h3>Highlight Data Format</h3>
        <p>Each highlight object should contain:</p>
        <ul>
            <li><strong>page</strong> (number): Page number (1-based)</li>
            <li><strong>x1, y1</strong> (number): Top-left corner coordinates in PDF units</li>
            <li><strong>x2, y2</strong> (number): Bottom-right corner coordinates in PDF units</li>
        </ul>
    </div>

    <div class="demo-section">
        <h2>Advanced Options</h2>
        <p>You can customize highlights with additional properties:</p>
        <div class="highlight-example">{
  "page": 1,
  "x1": 100, "y1": 200, "x2": 400, "y2": 250,
  "colorClass": "yellow",        // yellow, green, blue, red, orange, purple, pink
  "opacity": 0.4,               // 0.1 to 1.0
  "borderStyle": "dashed",      // solid, dashed, dotted, thick, thin
  "animation": "pulse",         // pulse, fade-in
  "title": "Important text",    // Tooltip text
  "id": "highlight-1",          // Custom identifier
  "accessible": true,           // Enable keyboard navigation
  "ariaLabel": "Highlighted text about..."
}</div>
    </div>

    <div class="demo-section">
        <h2>Demo Links</h2>
        <p>Click these links to see different highlight examples (you'll need to replace 'sample.pdf' with an actual PDF file):</p>
        
        <a href="viewer.html?file=sample.pdf&highlights=[{&quot;page&quot;:1,&quot;x1&quot;:100,&quot;y1&quot;:200,&quot;x2&quot;:400,&quot;y2&quot;:250}]" 
           class="demo-link">Basic Yellow Highlight</a>
        
        <a href="viewer.html?file=sample.pdf&highlights=[{&quot;page&quot;:1,&quot;x1&quot;:100,&quot;y1&quot;:200,&quot;x2&quot;:400,&quot;y2&quot;:250,&quot;colorClass&quot;:&quot;green&quot;}]" 
           class="demo-link">Green Highlight</a>
        
        <a href="viewer.html?file=sample.pdf&highlights=[{&quot;page&quot;:1,&quot;x1&quot;:100,&quot;y1&quot;:200,&quot;x2&quot;:400,&quot;y2&quot;:250,&quot;colorClass&quot;:&quot;blue&quot;,&quot;animation&quot;:&quot;pulse&quot;}]" 
           class="demo-link">Blue Pulsing Highlight</a>
        
        <a href="viewer.html?file=sample.pdf&highlights=[{&quot;page&quot;:1,&quot;x1&quot;:100,&quot;y1&quot;:200,&quot;x2&quot;:400,&quot;y2&quot;:250,&quot;colorClass&quot;:&quot;red&quot;,&quot;borderStyle&quot;:&quot;dashed&quot;,&quot;title&quot;:&quot;Important note&quot;}]" 
           class="demo-link">Red Dashed with Tooltip</a>
        
        <a href="viewer.html?file=sample.pdf&highlights=[{&quot;page&quot;:1,&quot;x1&quot;:50,&quot;y1&quot;:100,&quot;x2&quot;:200,&quot;y2&quot;:150,&quot;colorClass&quot;:&quot;yellow&quot;},{&quot;page&quot;:1,&quot;x1&quot;:300,&quot;y1&quot;:300,&quot;x2&quot;:500,&quot;y2&quot;:350,&quot;colorClass&quot;:&quot;green&quot;}]" 
           class="demo-link">Multiple Highlights</a>
    </div>

    <div class="demo-section">
        <h2>JavaScript API</h2>
        <p>You can also add highlights programmatically:</p>
        <div class="highlight-example">// Get the external highlight manager from PDFViewerApplication
const highlightManager = PDFViewerApplication.pdfViewer._externalHighlightManager;

// Add new highlights
highlightManager.addHighlights([
  {
    page: 1,
    x1: 100, y1: 200, x2: 400, y2: 250,
    colorClass: 'yellow',
    title: 'Added via JavaScript'
  }
]);

// Clear all highlights
highlightManager.clearHighlights();</div>
    </div>

    <div class="demo-section">
        <h2>Color Classes</h2>
        <p>Available color classes:</p>
        <ul>
            <li><strong>yellow</strong> - Default yellow highlight</li>
            <li><strong>green</strong> - Green highlight</li>
            <li><strong>blue</strong> - Blue highlight</li>
            <li><strong>red</strong> - Red highlight</li>
            <li><strong>orange</strong> - Orange highlight</li>
            <li><strong>purple</strong> - Purple highlight</li>
            <li><strong>pink</strong> - Pink highlight</li>
        </ul>
    </div>

    <div class="demo-section">
        <h2>Border Styles</h2>
        <p>Available border styles:</p>
        <ul>
            <li><strong>solid</strong> - Solid border (default)</li>
            <li><strong>dashed</strong> - Dashed border</li>
            <li><strong>dotted</strong> - Dotted border</li>
            <li><strong>thick</strong> - Thick border</li>
            <li><strong>thin</strong> - Thin border</li>
        </ul>
    </div>

    <div class="demo-section">
        <h2>Animations</h2>
        <p>Available animations:</p>
        <ul>
            <li><strong>pulse</strong> - Pulsing opacity effect</li>
            <li><strong>fade-in</strong> - Fade in when first displayed</li>
        </ul>
    </div>

    <div class="demo-section">
        <h2>Accessibility Features</h2>
        <p>Set <code>accessible: true</code> to enable:</p>
        <ul>
            <li>Keyboard navigation (Tab key)</li>
            <li>Screen reader support</li>
            <li>ARIA labels</li>
            <li>Focus indicators</li>
        </ul>
    </div>

    <div class="demo-section">
        <h2>URL Encoding</h2>
        <p>When passing highlights via URL, make sure to properly encode the JSON:</p>
        <div class="highlight-example">// JavaScript example
const highlights = [
  { page: 1, x1: 100, y1: 200, x2: 400, y2: 250, colorClass: 'yellow' }
];
const encodedHighlights = encodeURIComponent(JSON.stringify(highlights));
const url = `viewer.html?file=[SZCG2025000306-A]深圳大学电化学测试仪采购(1).pdf&highlights=${encodedHighlights}`;
window.open(url);</div>
    </div>

    <div class="note">
        <strong>Tips:</strong>
        <ul>
            <li>PDF coordinates start from bottom-left corner</li>
            <li>Use browser developer tools to inspect highlight elements</li>
            <li>Test with different zoom levels and rotations</li>
            <li>Highlights are responsive and adapt to page transformations</li>
        </ul>
    </div>
</body>
</html>
