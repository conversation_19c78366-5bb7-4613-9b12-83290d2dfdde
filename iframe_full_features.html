<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <title>PDF with Full Features</title>
</head>
<body>
    <h1>PDF查看器（包含批注功能）</h1>
    
    <button onclick="loadPDFWithFullFeatures()">加载完整功能PDF</button>
    
    <iframe 
        id="pdfViewer" 
        width="100%" 
        height="600px" 
        style="border: 1px solid #ccc;"
        sandbox="allow-scripts allow-same-origin allow-forms allow-downloads">
    </iframe>

    <script>
        function loadPDFWithFullFeatures() {
            const pdfFile = 'compressed.tracemonkey-pldi-09.pdf';
            const highlights = [{
                page: 1,
                x1: 100, y1: 200, x2: 400, y2: 250,
                colorClass: 'yellow'
            }];
            
            const encodedHighlights = encodeURIComponent(JSON.stringify(highlights));
            
            // 包含所有必要参数的URL
            const url = `build/generic/web/viewer.html?` +
                `file=${pdfFile}&` +
                `highlights=${encodedHighlights}&` +
                `enableAnnotations=true&` +
                `renderForms=true&` +
                `enableScripting=true&` +
                `textLayerMode=1&` +
                `enableComment=false&` +
                `annotationEditorMode=1&`+
                `annotationMode=2#page=1`;
                
            document.getElementById('pdfViewer').src = url;
        }
        
        // 页面加载时自动加载
        window.onload = loadPDFWithFullFeatures;
    </script>
</body>
</html>