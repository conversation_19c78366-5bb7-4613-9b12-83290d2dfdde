# PDF.js External Highlights Feature

This document describes the external highlights feature that allows you to display predefined highlight regions on PDF pages through URL parameters or JavaScript API.

## Overview

The external highlights feature enables you to:
- Pass highlight data via URL parameters
- Display highlights with various colors, styles, and animations
- Add tooltips and accessibility features
- Programmatically manage highlights via JavaScript API

## Quick Start

### Basic Usage

Add highlights to a PDF by appending the `highlights` parameter to the viewer URL:

```
viewer.html?file=document.pdf&highlights=[{"page":1,"x1":100,"y1":200,"x2":400,"y2":250}]
```

### Highlight Data Format

Each highlight object requires these properties:

- `page` (number): Page number (1-based)
- `x1`, `y1` (number): Top-left corner coordinates in PDF units
- `x2`, `y2` (number): Bottom-right corner coordinates in PDF units

## Advanced Options

### Styling Options

```javascript
{
  "page": 1,
  "x1": 100, "y1": 200, "x2": 400, "y2": 250,
  
  // Color options
  "colorClass": "yellow",           // Predefined color class
  "color": "rgba(255,0,0,0.3)",    // Custom background color
  "borderColor": "rgba(255,0,0,0.8)", // Custom border color
  
  // Border options
  "borderWidth": 2,                 // Border width in pixels
  "borderStyle": "dashed",          // solid, dashed, dotted, thick, thin
  "borderRadius": 5,                // Border radius in pixels
  
  // Opacity and effects
  "opacity": 0.4,                   // Overall opacity (0.1 to 1.0)
  "animation": "pulse",             // pulse, fade-in
  
  // Identification and accessibility
  "id": "highlight-1",              // Custom identifier
  "title": "Important text",        // Tooltip text
  "accessible": true,               // Enable keyboard navigation
  "ariaLabel": "Highlighted section", // Screen reader label
  
  // Custom CSS classes
  "className": "my-custom-class"    // Additional CSS classes
}
```

### Color Classes

Available predefined color classes:
- `yellow` (default)
- `green`
- `blue`
- `red`
- `orange`
- `purple`
- `pink`

### Border Styles

- `solid` (default)
- `dashed`
- `dotted`
- `thick`
- `thin`

### Animations

- `pulse` - Pulsing opacity effect
- `fade-in` - Fade in when first displayed

## JavaScript API

### Accessing the Highlight Manager

```javascript
const highlightManager = PDFViewerApplication.pdfViewer._externalHighlightManager;
```

### Adding Highlights Programmatically

```javascript
highlightManager.addHighlights([
  {
    page: 1,
    x1: 100, y1: 200, x2: 400, y2: 250,
    colorClass: 'yellow',
    title: 'Added via JavaScript'
  }
]);
```

### Managing Highlights

```javascript
// Check if highlights are enabled
if (highlightManager.enabled) {
  console.log('External highlights are active');
}

// Get all highlights
const allHighlights = highlightManager.getAllHighlights();

// Get highlights for specific page
const pageHighlights = highlightManager.getHighlightsForPage(1);

// Check if page has highlights
if (highlightManager.hasHighlightsForPage(1)) {
  console.log('Page 1 has highlights');
}

// Clear all highlights
highlightManager.clearHighlights();

// Update highlights after page transformation
highlightManager.updateHighlights(1);
```

## Examples

### Multiple Highlights

```javascript
const highlights = [
  {
    page: 1,
    x1: 100, y1: 200, x2: 400, y2: 250,
    colorClass: 'yellow',
    title: 'First highlight'
  },
  {
    page: 1,
    x1: 100, y1: 300, x2: 350, y2: 340,
    colorClass: 'green',
    animation: 'pulse',
    title: 'Second highlight'
  },
  {
    page: 2,
    x1: 50, y1: 100, x2: 300, y2: 150,
    colorClass: 'blue',
    accessible: true,
    ariaLabel: 'Important section on page 2'
  }
];

const url = `viewer.html?file=document.pdf&highlights=${encodeURIComponent(JSON.stringify(highlights))}`;
```

### Custom Styled Highlight

```javascript
const customHighlight = {
  page: 1,
  x1: 100, y1: 400, x2: 300, y2: 440,
  color: 'rgba(255, 165, 0, 0.4)',
  borderColor: 'rgba(255, 165, 0, 0.9)',
  borderWidth: 3,
  borderRadius: 8,
  title: 'Custom orange highlight',
  className: 'my-special-highlight'
};
```

## CSS Customization

You can customize highlight appearance by modifying `external_highlights.css` or adding your own styles:

```css
.external-highlight.my-custom-class {
  background: linear-gradient(45deg, rgba(255,0,0,0.3), rgba(0,255,0,0.3));
  border: 2px solid #333;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.external-highlight.highlight-glow {
  box-shadow: 0 0 10px rgba(255, 255, 0, 0.8);
}
```

## Coordinate System

PDF.js uses the PDF coordinate system where:
- Origin (0,0) is at the bottom-left corner of the page
- X increases to the right
- Y increases upward
- Units are in PDF points (1/72 inch)

To convert from screen coordinates to PDF coordinates, you can use:

```javascript
const pageView = PDFViewerApplication.pdfViewer.getPageView(pageNumber - 1);
const [pdfX, pdfY] = pageView.getPagePoint(screenX, screenY);
```

## Browser Support

The external highlights feature supports:
- Modern browsers with ES6+ support
- Responsive design (mobile and desktop)
- High contrast mode
- Dark mode
- Print styles
- Screen readers and keyboard navigation

## Performance Considerations

- Highlights are rendered as DOM elements, not canvas overlays
- Large numbers of highlights (>100 per page) may impact performance
- Highlights automatically update when pages are zoomed or rotated
- Memory usage scales with the number of active highlights

## Troubleshooting

### Common Issues

1. **Highlights not appearing**: Check that the JSON is properly formatted and URL-encoded
2. **Wrong position**: Verify PDF coordinates (remember Y axis is flipped)
3. **Styling not applied**: Ensure CSS classes are correctly spelled
4. **Performance issues**: Reduce number of highlights or simplify styles

### Debug Mode

Enable debug logging:

```javascript
// Add to browser console
localStorage.setItem('pdfjs.verbosity', '5');
```

### Testing

Use the included test files:
- `highlight_demo.html` - Interactive demo and documentation
- `test_highlights.js` - Automated testing script

## Files Modified/Added

- `web/external_highlight_manager.js` - Main highlight manager class
- `web/external_highlights.css` - Highlight styles
- `web/pdf_page_view.js` - Integration with page rendering
- `web/pdf_viewer.js` - Integration with viewer
- `web/app.js` - Application initialization
- `web/viewer.html` - CSS inclusion
- `web/highlight_demo.html` - Demo and documentation
- `web/test_highlights.js` - Testing utilities

## License

This feature is part of PDF.js and is licensed under the Apache License 2.0.
