name: Lint Fluent Reference Files
on:
  push:
    paths:
      - 'l10n/en-US/**.ftl'
      - '.github/fluent_linter_config.yml'
      - '.github/workflows/fluent_linter.yml'
    branches:
      - master
  pull_request:
    paths:
      - 'l10n/en-US/**.ftl'
      - '.github/fluent_linter_config.yml'
      - '.github/workflows/fluent_linter.yml'
    branches:
      - master
  workflow_dispatch:
permissions:
  contents: read

jobs:
  lint:
    name: Lint
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Use Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: '3.13'
          cache: 'pip'

      - name: Install Fluent dependencies
        run: |
          pip install -r .github/requirements.txt

      - name: Lint Fluent reference files
        run: |
          moz-fluent-lint ./l10n/en-US --config .github/fluent_linter_config.yml
