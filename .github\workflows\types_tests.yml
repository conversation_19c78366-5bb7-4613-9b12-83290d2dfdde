name: Types tests
on: [push, pull_request]
permissions:
  contents: read

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        node-version: [lts/*]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install dependencies
        run: npm ci

      - name: Run types tests
        run: npx gulp typestest
