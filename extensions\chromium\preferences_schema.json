{"type": "object", "properties": {"viewerCssTheme": {"title": "Theme", "description": "The theme to use.\n0 = Use system theme.\n1 = Light theme.\n2 = Dark theme.", "type": "integer", "enum": [0, 1, 2], "default": 2}, "showPreviousViewOnLoad": {"description": "DEPRECATED. Set viewOnLoad to 1 to disable showing the last page/position on load.", "type": "boolean", "default": true}, "viewOnLoad": {"title": "View position on load", "description": "The position in the document upon load.\n -1 = Default (uses OpenAction if available, otherwise equal to `viewOnLoad = 0`).\n 0 = The last viewed page/position.\n 1 = The initial page/position.", "type": "integer", "enum": [-1, 0, 1], "default": 0}, "defaultZoomDelay": {"title": "Default zoom delay", "description": "Delay (in ms) to wait before redrawing the canvas.", "type": "integer", "default": 400}, "defaultZoomValue": {"title": "Default zoom level", "description": "Default zoom level of the viewer. Accepted values: 'auto', 'page-actual', 'page-width', 'page-height', 'page-fit', or a zoom level in percents.", "type": "string", "pattern": "|auto|page-actual|page-width|page-height|page-fit|[0-9]+\\.?[0-9]*(,[0-9]+\\.?[0-9]*){0,2}", "default": ""}, "sidebarViewOnLoad": {"title": "Sidebar state on load", "description": "Controls the state of the sidebar upon load.\n -1 = Default (uses PageMode if available, otherwise the last position if available/enabled).\n 0 = Do not show sidebar.\n 1 = Show thumbnails in sidebar.\n 2 = Show document outline in sidebar.\n 3 = Show attachments in sidebar.", "type": "integer", "enum": [-1, 0, 1, 2, 3], "default": -1}, "enableHandToolOnLoad": {"description": "DEPRECATED. Set cursorToolOnLoad to 1 to enable the hand tool by default.", "type": "boolean", "default": false}, "enableHWA": {"title": "Enable hardware acceleration", "description": "Whether to enable hardware acceleration.", "type": "boolean", "default": true}, "enableAltText": {"type": "boolean", "default": false}, "enableGuessAltText": {"type": "boolean", "default": true}, "enableAltTextModelDownload": {"type": "boolean", "default": true}, "enableNewAltTextWhenAddingImage": {"type": "boolean", "default": true}, "altTextLearnMoreUrl": {"type": "string", "default": ""}, "enableSignatureEditor": {"type": "boolean", "default": false}, "enableUpdatedAddImage": {"type": "boolean", "default": false}, "cursorToolOnLoad": {"title": "Cursor tool on load", "description": "The cursor tool that is enabled upon load.\n 0 = Text selection tool.\n 1 = Hand tool.", "type": "integer", "enum": [0, 1], "default": 0}, "pdfBugEnabled": {"title": "Enable debugging tools", "description": "Whether to enable debugging tools.", "type": "boolean", "default": false}, "enableScripting": {"title": "Enable active content (JavaScript) in PDFs", "type": "boolean", "description": "Whether to allow execution of active content (JavaScript) by PDF files.", "default": false}, "enableHighlightFloatingButton": {"type": "boolean", "default": false}, "highlightEditorColors": {"type": "string", "default": "yellow=#FFFF98,green=#53FFBC,blue=#80EBFF,pink=#FFCBE6,red=#FF4F5F,yellow_HCM=#FFFFCC,green_HCM=#53FFBC,blue_HCM=#80EBFF,pink_HCM=#F6B8FF,red_HCM=#C50043"}, "disableRange": {"title": "Disable range requests", "description": "Whether to disable range requests (not recommended).", "type": "boolean", "default": false}, "disableStream": {"title": "Disable streaming for requests", "description": "Whether to disable streaming for requests (not recommended).", "type": "boolean", "default": false}, "disableAutoFetch": {"type": "boolean", "default": false}, "disableFontFace": {"title": "Disable @font-face", "description": "Whether to disable @font-face and fall back to canvas rendering (this is more resource-intensive).", "type": "boolean", "default": false}, "disableTextLayer": {"description": "DEPRECATED. Set textLayerMode to 0 to disable the text selection layer by default.", "type": "boolean", "default": false}, "textLayerMode": {"title": "Text layer mode", "description": "Controls if the text layer is enabled, and the selection mode that is used.\n 0 = Disabled.\n 1 = Enabled.", "type": "integer", "enum": [0, 1], "default": 1}, "externalLinkTarget": {"title": "External links target window", "description": "Controls how external links will be opened.\n 0 = default.\n 1 = replaces current window.\n 2 = new window/tab.\n 3 = parent.\n 4 = in top window.", "type": "integer", "enum": [0, 1, 2, 3, 4], "default": 0}, "disablePageLabels": {"type": "boolean", "default": false}, "disablePageMode": {"description": "DEPRECATED.", "type": "boolean", "default": false}, "disableTelemetry": {"title": "Disable telemetry", "type": "boolean", "description": "Whether to prevent the extension from reporting the extension and browser version to the extension developers.", "default": false}, "annotationMode": {"type": "integer", "enum": [0, 1, 2, 3], "default": 2}, "annotationEditorMode": {"type": "integer", "enum": [-1, 0, 3, 15], "default": 0}, "capCanvasAreaFactor": {"type": "integer", "default": 200}, "enablePermissions": {"type": "boolean", "default": false}, "enableXfa": {"type": "boolean", "default": true}, "historyUpdateUrl": {"type": "boolean", "default": false}, "ignoreDestinationZoom": {"title": "Ignore the zoom argument in destinations", "description": "When enabled it will maintain the currently active zoom level, rather than letting the PDF document modify it, when navigating to internal destinations.", "type": "boolean", "default": false}, "enablePrintAutoRotate": {"title": "Automatically rotate printed pages", "description": "When enabled, landscape pages are rotated when printed.", "type": "boolean", "default": true}, "scrollModeOnLoad": {"title": "Scroll mode on load", "description": "Controls how the viewer scrolls upon load.\n -1 = Default (uses the last position if available/enabled).\n 3 = Page scrolling.\n 0 = Vertical scrolling.\n 1 = Horizontal scrolling.\n 2 = Wrapped scrolling.", "type": "integer", "enum": [-1, 0, 1, 2, 3], "default": -1}, "spreadModeOnLoad": {"title": "Spread mode on load", "description": "Whether the viewer should join pages into spreads upon load.\n -1 = Default (uses the last position if available/enabled).\n 0 = No spreads.\n 1 = Odd spreads.\n 2 = Even spreads.", "type": "integer", "enum": [-1, 0, 1, 2], "default": -1}, "forcePageColors": {"description": "When enabled, the pdf rendering will use the high contrast mode colors", "type": "boolean", "default": false}, "pageColorsBackground": {"description": "The color is a string as defined in CSS. Its goal is to help improve readability in high contrast mode", "type": "string", "default": "<PERSON><PERSON>"}, "pageColorsForeground": {"description": "The color is a string as defined in CSS. Its goal is to help improve readability in high contrast mode", "type": "string", "default": "CanvasText"}, "enableAutoLinking": {"description": "Enable creation of hyperlinks from text that look like URLs.", "type": "boolean", "default": true}, "enableComment": {"description": "Enable creation of comment annotations.", "type": "boolean", "default": false}}}