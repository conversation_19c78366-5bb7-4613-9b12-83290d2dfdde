name: Publish release
on:
  release:
    types: [published]
permissions:
  contents: read
  id-token: write

jobs:
  publish:
    name: Publish
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [lts/*]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: 'https://registry.npmjs.org'

      - name: Install dependencies
        run: npm ci

      - name: Build the `pdfjs-dist` library
        run: npx gulp dist

      - name: Publish the `pdfjs-dist` library to NPM
        run: npm publish ./build/dist --provenance
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
