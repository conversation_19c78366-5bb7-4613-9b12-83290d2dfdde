name: Font tests
on:
  push:
    paths:
      - 'gulpfile.mjs'
      - 'src/**'
      - 'test/test.mjs'
      - 'test/font/**'
      - '.github/workflows/font_tests.yml'
    branches:
      - master
  pull_request:
    paths:
      - 'gulpfile.mjs'
      - 'src/**'
      - 'test/test.mjs'
      - 'test/font/**'
      - '.github/workflows/font_tests.yml'
    branches:
      - master
  workflow_dispatch:
permissions:
  contents: read

jobs:
  test:
    name: Test

    strategy:
      fail-fast: false
      matrix:
        node-version: [lts/*]
        os: [windows-latest, ubuntu-latest]

    runs-on: ${{ matrix.os }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install dependencies
        run: npm ci

      - name: Use Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: '3.13'
          cache: 'pip'

      - name: Install Fonttools
        run: pip install fonttools

      - name: Run font tests
        run: npx gulp fonttest --headless
